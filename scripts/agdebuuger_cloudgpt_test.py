from autogen_agentchat.teams import MagenticOneGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.agents.web_surfer import MultimodalWebSurfer
from autogen_ext.models.openai import AzureOpenAIChatCompletionClient

async def get_agent_team():
    # 使用AzureOpenAIChatCompletionClient会自动触发CloudGPT配置
    model_client = AzureOpenAIChatCompletionClient(
        model="gpt-4o-20241120",  # 使用CloudGPT支持的模型名称
        azure_deployment="gpt-4o",  # 可选
    )

    surfer = MultimodalWebSurfer(
        "WebSurfer",
        model_client=model_client,
    )
    team = MagenticOneGroupChat([surfer], model_client=model_client)

    return team